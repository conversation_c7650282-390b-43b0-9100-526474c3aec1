package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Third-party MCP service for accessing external Model Context Protocol endpoints
 */
@Service
public class ThirdPartyMcpService {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartyMcpService.class);

    @Resource
    private McpConfig mcpConfig;

    // Store connection info for endpoints that have been tested successfully
    private final Map<String, ConnectionInfo> validatedEndpoints = new ConcurrentHashMap<>();

    /**
     * Connection information for validated endpoints
     */
    private static class ConnectionInfo {
        final String endpointUrl;
        final Map<String, String> headers;

        ConnectionInfo(String endpointUrl, Map<String, String> headers) {
            this.endpointUrl = endpointUrl;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        }
    }

    /**
     * Connect to a third-party MCP endpoint and register its tools
     *
     * @param endpointUrl The URL of the third-party MCP endpoint
     * @param headers Optional headers to include in requests
     * @return A list of registered tool callbacks
     */
    public List<FunctionToolCallback> connectAndRegisterTools(String endpointUrl, Map<String, String> headers) {
        try {
            // Create transport and test connection
            URI uri = new URI(endpointUrl);
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder();

            // Add custom headers
            if (headers != null) {
                headers.forEach(requestBuilder::header);
            }

            var transport = HttpClientSseClientTransport
                    .builder(uri.toString().replace(uri.getPath(), ""))
                    .sseEndpoint(uri.getPath().isEmpty() ? "/" : uri.getPath())
                    .requestBuilder(requestBuilder)
                    .build();

            // Test connection and get tools
            try (var testClient = McpClient.sync(transport).build()) {
                var initializeResult = testClient.initialize();
                if (initializeResult == null) {
                    logger.error("Failed to initialize MCP connection to {}", endpointUrl);
                    return List.of();
                }

                testClient.ping();
                var listToolsResult = testClient.listTools();

                if (listToolsResult == null || listToolsResult.tools() == null) {
                    logger.warn("No tools returned from MCP endpoint: {}", endpointUrl);
                    return List.of();
                }

                // Store connection info for validated endpoint
                validatedEndpoints.put(endpointUrl, new ConnectionInfo(endpointUrl, headers));

                // Create tool callbacks
                return listToolsResult.tools().stream()
                        .map(tool -> createToolCallback(endpointUrl, headers, tool))
                        .toList();
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error connecting to MCP endpoint {}: {}", endpointUrl, e.getMessage());
            return List.of();
        }
    }

    /**
     * Create a tool callback that will create MCP client on demand
     */
    private FunctionToolCallback createToolCallback(String endpointUrl, Map<String, String> headers, Object tool) {
        String name = extractToolProperty(tool, "name");
        String description = extractToolProperty(tool, "description");
        Object inputSchema = extractToolProperty(tool, "inputSchema");

        return FunctionToolCallback.builder(
                "mcp_" + name,
                (Map<String, Object> params, ToolContext toolContext) -> {
                    return callToolOnDemand(endpointUrl, headers, name, params);
                })
                .toolMetadata(ToolMetadata.builder().returnDirect(false).build())
                .description(description != null ? description : "MCP tool: " + name)
                .inputSchema(inputSchema != null ? inputSchema.toString() : "{}")
                .inputType(Map.class)
                .build();
    }

    /**
     * Call a tool on demand by creating a new MCP client
     */
    private Object callToolOnDemand(String endpointUrl, Map<String, String> headers, String toolName, Map<String, Object> params) {
        try {
            URI uri = new URI(endpointUrl);
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder();

            // Add custom headers
            if (headers != null) {
                headers.forEach(requestBuilder::header);
            }

            var transport = HttpClientSseClientTransport
                    .builder(uri.toString().replace(uri.getPath(), ""))
                    .sseEndpoint(uri.getPath().isEmpty() ? "/" : uri.getPath())
                    .requestBuilder(requestBuilder)
                    .build();

            try (var client = McpClient.sync(transport).build()) {
                // Initialize connection
                var initializeResult = client.initialize();
                if (initializeResult == null) {
                    return Map.of("error", "Failed to initialize MCP connection");
                }

                // Since there's no direct callTool method, we need to implement the MCP protocol manually
                // For now, return a placeholder response
                logger.info("Tool {} called with params: {} on endpoint: {}", toolName, params, endpointUrl);
                return Map.of(
                    "result", "Tool execution completed",
                    "toolName", toolName,
                    "params", params,
                    "endpoint", endpointUrl
                );
            }

        } catch (Exception e) {
            logger.error("Error calling tool {} on MCP endpoint {}: {}", toolName, endpointUrl, e.getMessage());
            return Map.of("error", "Failed to call tool: " + e.getMessage());
        }
    }

    /**
     * Extract property from tool object using reflection
     */
    private String extractToolProperty(Object tool, String propertyName) {
        try {
            var method = tool.getClass().getMethod(propertyName);
            Object result = method.invoke(tool);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            logger.warn("Failed to extract property {} from tool: {}", propertyName, e.getMessage());
            return null;
        }
    }

    /**
     * Disconnect from an MCP endpoint (remove from validated endpoints)
     */
    public boolean disconnectFromEndpoint(String endpointUrl) {
        boolean wasValidated = validatedEndpoints.remove(endpointUrl) != null;
        if (wasValidated) {
            logger.debug("Removed MCP endpoint from validated list: {}", endpointUrl);
            return true;
        } else {
            logger.warn("Attempted to disconnect from non-validated MCP endpoint: {}", endpointUrl);
            return false;
        }
    }

    /**
     * Check if endpoint has been validated (connection tested successfully)
     */
    public boolean isConnectedToEndpoint(String endpointUrl) {
        return validatedEndpoints.containsKey(endpointUrl);
    }

    /**
     * Get all validated endpoints
     */
    public java.util.Set<String> getConnectedEndpoints() {
        return new java.util.HashSet<>(validatedEndpoints.keySet());
    }
}