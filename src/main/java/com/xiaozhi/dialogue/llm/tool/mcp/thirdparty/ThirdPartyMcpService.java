package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Third-party MCP service for accessing external Model Context Protocol endpoints
 */
@Service
public class ThirdPartyMcpService {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartyMcpService.class);

    @Resource
    private McpConfig mcpConfig;

    // Store active WebClients by endpoint URL
    private final Map<String, WebClient> activeClients = new ConcurrentHashMap<>();

    /**
     * Connect to a third-party MCP endpoint and register its tools
     *
     * @param endpointUrl The URL of the third-party MCP endpoint
     * @param headers Optional headers to include in requests
     * @return A list of registered tool callbacks
     */
    public List<FunctionToolCallback> connectAndRegisterTools(String endpointUrl, Map<String, String> headers) {
        WebClient webClient = createWebClient(endpointUrl, headers);

        // Store the client for future use
        activeClients.put(endpointUrl, webClient);

        // Initialize the MCP connection
        Map<String, Object> initializeResult = initializeConnection(webClient);
        if (initializeResult == null) {
            logger.error("Failed to initialize MCP connection to {}", endpointUrl);
            return List.of();
        }

        // Get tools list and register them
        return getAndRegisterTools(webClient, endpointUrl);
    }

    /**
     * Create a WebClient for the given endpoint
     */
    private WebClient createWebClient(String endpointUrl, Map<String, String> headers) {
        WebClient.Builder builder = WebClient.builder()
                .baseUrl(endpointUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        // Add any custom headers
        if (headers != null) {
            headers.forEach(builder::defaultHeader);
        }

        return builder.build();
    }

    /**
     * Initialize connection to the MCP endpoint
     */
    private Map<String, Object> initializeConnection(WebClient webClient) {
        try {
            Map<String, Object> initializeRequest = Map.of(
                "jsonrpc", "2.0",
                "id", System.currentTimeMillis(),
                "method", "initialize",
                "params", Map.of()
            );

            String responseJson = webClient.post()
                    .bodyValue(initializeRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
                    .block();

            return JsonUtil.fromJson(responseJson, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
        } catch (WebClientResponseException e) {
            logger.error("Error initializing MCP connection: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            logger.error("Unexpected error initializing MCP connection", e);
            return null;
        }
    }

    /**
     * Get tools from the MCP endpoint and register them as function callbacks
     */
    private List<FunctionToolCallback> getAndRegisterTools(WebClient webClient, String endpointUrl) {

        try {
            Map<String, Object> toolsRequest = Map.of(
                "jsonrpc", "2.0",
                "id", System.currentTimeMillis(),
                "method", "tools/list",
                "params", Map.of()
            );

            String responseJson = webClient.post()
                    .bodyValue(toolsRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
                    .block();

            Map<String, Object> response = JsonUtil.fromJson(responseJson, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

            if (response.containsKey("result")) {
                Map<String, Object> result = (Map<String, Object>) response.get("result");
                List<Map<String, Object>> tools = (List<Map<String, Object>>) result.get("tools");

                return tools.stream()
                        .map(tool -> createToolCallback(webClient, tool, endpointUrl))
                        .toList();
            }
        } catch (WebClientResponseException e) {
            logger.error("Error getting tools from MCP endpoint {}: {}", endpointUrl, e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error getting tools from MCP endpoint {}", endpointUrl, e);
        }

        return List.of();
    }

    /**
     * Create a FunctionToolCallback for a tool from the MCP endpoint
     */
    private FunctionToolCallback createToolCallback(WebClient webClient, Map<String, Object> tool, String endpointUrl) {
        String name = (String) tool.get("name");
        String description = (String) tool.get("description");
        Object inputSchema = tool.get("inputSchema");

        return FunctionToolCallback.builder(
                "mcp_" + name,
                (Map<String, Object> params, ToolContext toolContext) -> {
                    return callTool(webClient, name, params, endpointUrl);
                })
                .toolMetadata(ToolMetadata.builder().returnDirect(false).build())
                .description(description)
                .inputSchema(JsonUtil.toJson(inputSchema))
                .inputType(Map.class)
                .build();
    }

    /**
     * Call a tool on the MCP endpoint
     */
    private Object callTool(WebClient webClient, String toolName, Map<String, Object> params, String endpointUrl) {
        try {
            Map<String, Object> toolCallRequest = Map.of(
                "jsonrpc", "2.0",
                "id", System.currentTimeMillis(),
                "method", "tools/call",
                "params", Map.of(
                    "name", toolName,
                    "arguments", params
                )
            );

            String responseJson = webClient.post()
                    .bodyValue(toolCallRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(mcpConfig.getConnection().getTimeoutMs()))
                    .block();

            Map<String, Object> response = JsonUtil.fromJson(responseJson, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

            if (response.containsKey("result")) {
                return response.get("result");
            } else if (response.containsKey("error")) {
                return response.get("error");
            }
        } catch (WebClientResponseException e) {
            logger.error("Error calling tool {} on MCP endpoint {}: {}", toolName, endpointUrl, e.getMessage());
            return Map.of("error", "Failed to call tool: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error calling tool {} on MCP endpoint {}", toolName, endpointUrl, e);
            return Map.of("error", "Unexpected error: " + e.getMessage());
        }

        return Map.of("error", "No result or error returned");
    }

    /**
     * Disconnect from an MCP endpoint
     */
    public boolean disconnectFromEndpoint(String endpointUrl) {
        WebClient removed = activeClients.remove(endpointUrl);
        if (removed != null) {
            logger.debug("Disconnected from MCP endpoint: {}", endpointUrl);
            return true;
        } else {
            logger.warn("Attempted to disconnect from non-connected MCP endpoint: {}", endpointUrl);
            return false;
        }
    }

    /**
     * Check if connected to an MCP endpoint
     */
    public boolean isConnectedToEndpoint(String endpointUrl) {
        return activeClients.containsKey(endpointUrl);
    }

    /**
     * Get all connected endpoints
     */
    public java.util.Set<String> getConnectedEndpoints() {
        return new java.util.HashSet<>(activeClients.keySet());
    }
}